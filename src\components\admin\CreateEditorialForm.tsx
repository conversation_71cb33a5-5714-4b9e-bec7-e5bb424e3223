import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';
import { Plus, Trash2, Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

interface EditorialEntry {
  id: string;
  brand: string;
  subSector: string;
  publication: string;
  placement: string;
  title: string;
  page: number;
  link: string;
  reporter: string;
  country: string;
  language: string;
  spokesperson: string;
  activity: string;
  mediaType: string;
  onlineChannel?: string;
  circulation?: number;
  audienceReach?: number;
  pageSize?: string;
}

interface StaticData {
  date: string;
  company: string;
  industry: string;
}

interface CreateEditorialFormProps {
  onSave: (editorials: any[]) => void;
  onCancel: () => void;
  initialData?: any | null;
}

export const CreateEditorialForm: React.FC<CreateEditorialFormProps> = ({
  onSave,
  onCancel,
  initialData
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // Static data (first row) - auto-populated
  const [staticData, setStaticData] = useState<StaticData>({
    date: format(new Date(), 'yyyy-MM-dd'),
    company: '',
    industry: ''
  });

  // Dynamic entries (second and third rows) - cloneable
  const [entries, setEntries] = useState<EditorialEntry[]>([
    createEmptyEntry(),
    createEmptyEntry()
  ]);

  // Create empty entry with unique ID
  function createEmptyEntry(): EditorialEntry {
    return {
      id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      brand: '',
      subSector: '',
      publication: '',
      placement: '',
      title: '',
      page: 1,
      link: '',
      reporter: '',
      country: '',
      language: '',
      spokesperson: '',
      activity: '',
      mediaType: '',
      onlineChannel: '',
      circulation: 0,
      audienceReach: 0,
      pageSize: ''
    };
  }

  // Add new entry (clone second and third row)
  const addEntry = () => {
    setEntries(prev => [...prev, createEmptyEntry(), createEmptyEntry()]);
  };

  // Remove entry
  const removeEntry = (id: string) => {
    setEntries(prev => prev.filter(entry => entry.id !== id));
  };

  // Update entry field
  const updateEntry = (id: string, field: keyof EditorialEntry, value: any) => {
    setEntries(prev => prev.map(entry =>
      entry.id === id ? { ...entry, [field]: value } : entry
    ));
  };

  // Update static data
  const updateStaticData = (field: keyof StaticData, value: string) => {
    setStaticData(prev => ({ ...prev, [field]: value }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Combine static data with all entries
      const editorials = entries.map(entry => ({
        ...staticData,
        ...entry,
        date: staticData.date,
        company: staticData.company,
        industry: staticData.industry
      }));

      await onSave(editorials);
    } catch (error) {
      console.error('Error saving editorials:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const industryOptions = ['Financial Services', 'Technology', 'Healthcare', 'Manufacturing', 'Retail', 'Education', 'Media', 'Other'];
  const mediaTypeOptions = ['Print', 'Online', 'Broadcast', 'Social Media'];
  const languageOptions = ['English', 'French', 'Spanish', 'German', 'Other'];
  const countryOptions = ['Nigeria', 'Ghana', 'Kenya', 'South Africa', 'Egypt', 'Other'];

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Create Editorial Entries</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              `Save ${entries.length} Entries`
            )}
          </Button>
        </div>
      </div>

      {/* Horizontal Scroll Container */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full w-full">
          <div className="min-w-max space-y-6 pb-6">

            {/* First Row - Static Data */}
            <Card className="w-full">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg flex items-center gap-2">
                  📅 Static Information
                  <span className="text-sm font-normal text-gray-500">(Applied to all entries)</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-6 min-w-[900px]">
                  <div className="space-y-2">
                    <Label>Date <span className="text-red-500">*</span></Label>
                    <Input
                      type="date"
                      value={staticData.date}
                      onChange={(e) => updateStaticData('date', e.target.value)}
                      className="bg-gray-50 border-gray-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Company <span className="text-red-500">*</span></Label>
                    <Input
                      placeholder="Enter company name"
                      value={staticData.company}
                      onChange={(e) => updateStaticData('company', e.target.value)}
                      className="bg-gray-50 border-gray-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Industry <span className="text-red-500">*</span></Label>
                    <Select value={staticData.industry} onValueChange={(value) => updateStaticData('industry', value)}>
                      <SelectTrigger className="bg-gray-50 border-gray-200">
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                      <SelectContent>
                        {industryOptions.map(option => (
                          <SelectItem key={option} value={option}>{option}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Dynamic Entries Section */}
            <Card className="w-full">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Button
                    type="button"
                    size="sm"
                    onClick={addEntry}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Entry Pair
                  </Button>
                  <span>Editorial Entries ({entries.length} entries)</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {entries.map((entry, index) => (
                    <div key={entry.id} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-medium">Entry {index + 1}</h4>
                        {entries.length > 2 && (
                          <Button
                            type="button"
                            size="sm"
                            variant="destructive"
                            onClick={() => removeEntry(entry.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      {/* Entry Fields - Horizontal Layout */}
                      <div className="grid grid-cols-6 gap-4 min-w-[1800px]">
                        <div className="space-y-2">
                          <Label>Brand</Label>
                          <Input
                            placeholder="Brand name"
                            value={entry.brand}
                            onChange={(e) => updateEntry(entry.id, 'brand', e.target.value)}
                            className="bg-white border-gray-200"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Sub Sector</Label>
                          <Input
                            placeholder="Sub sector"
                            value={entry.subSector}
                            onChange={(e) => updateEntry(entry.id, 'subSector', e.target.value)}
                            className="bg-white border-gray-200"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Publication</Label>
                          <Input
                            placeholder="Publication name"
                            value={entry.publication}
                            onChange={(e) => updateEntry(entry.id, 'publication', e.target.value)}
                            className="bg-white border-gray-200"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Placement</Label>
                          <Input
                            placeholder="Placement"
                            value={entry.placement}
                            onChange={(e) => updateEntry(entry.id, 'placement', e.target.value)}
                            className="bg-white border-gray-200"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Title</Label>
                          <Input
                            placeholder="Article title"
                            value={entry.title}
                            onChange={(e) => updateEntry(entry.id, 'title', e.target.value)}
                            className="bg-white border-gray-200"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Page</Label>
                          <Input
                            type="number"
                            placeholder="Page number"
                            value={entry.page}
                            onChange={(e) => updateEntry(entry.id, 'page', parseInt(e.target.value) || 1)}
                            className="bg-white border-gray-200"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
